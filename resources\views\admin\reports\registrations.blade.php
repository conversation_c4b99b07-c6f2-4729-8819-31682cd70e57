@extends('admin.dashboard')
@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">

            <!-- start page title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-flex align-items-center justify-content-between">
                        <h4 class="mb-0">Course Registration Reports</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('admin.reports.index') }}">Reports</a></li>
                                <li class="breadcrumb-item active">Registrations</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end page title -->

            <!-- Filters Card -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title mb-4">Filter Reports</h4>
                            <form method="GET" action="{{ route('admin.reports.registrations') }}" class="row g-3" id="filterForm">
                                <div class="col-md-3">
                                    <label for="course_id" class="form-label">Course</label>
                                    <select class="form-select" id="course_id" name="course_id">
                                        <option value="">All Courses</option>
                                        @foreach($allCourses as $course)
                                            <option value="{{ $course->id }}" {{ request('course_id') == $course->id ? 'selected' : '' }}>
                                                {{ $course->title }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="date_preset" class="form-label">Quick Date Range</label>
                                    <select class="form-select" id="date_preset" onchange="applyDatePreset()">
                                        <option value="">Custom Range</option>
                                        <option value="today">Today</option>
                                        <option value="yesterday">Yesterday</option>
                                        <option value="last_7_days">Last 7 Days</option>
                                        <option value="last_30_days">Last 30 Days</option>
                                        <option value="this_month">This Month</option>
                                        <option value="last_month">Last Month</option>
                                        <option value="this_quarter">This Quarter</option>
                                        <option value="this_year">This Year</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="date_from" class="form-label">From Date</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request('date_from') }}">
                                </div>
                                <div class="col-md-2">
                                    <label for="date_to" class="form-label">To Date</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request('date_to') }}">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid gap-2 d-md-flex">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="uil-filter me-1"></i> Apply Filters
                                        </button>
                                        <a href="{{ route('admin.reports.registrations') }}" class="btn btn-outline-secondary">
                                            <i class="uil-refresh me-1"></i> Reset
                                        </a>
                                        <button type="button" class="btn btn-success" onclick="exportData()">
                                            <i class="uil-export me-1"></i> Export
                                        </button>
                                    </div>
                                </div>

                                <!-- Advanced Search Row -->
                                <div class="col-12">
                                    <div class="border-top pt-3">
                                        <div class="row g-3">
                                            <div class="col-md-4">
                                                <label for="search_term" class="form-label">Search Students</label>
                                                <input type="text" class="form-control" id="search_term" name="search_term"
                                                       placeholder="Search by name, email, ID No, or KMLTTB No"
                                                       value="{{ request('search_term') }}">
                                            </div>
                                            <div class="col-md-2">
                                                <label for="enrollment_status" class="form-label">Status</label>
                                                <select class="form-select" id="enrollment_status" name="enrollment_status">
                                                    <option value="">All Status</option>
                                                    <option value="enrolled" {{ request('enrollment_status') == 'enrolled' ? 'selected' : '' }}>Enrolled</option>
                                                    <option value="in_progress" {{ request('enrollment_status') == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                                    <option value="completed" {{ request('enrollment_status') == 'completed' ? 'selected' : '' }}>Completed</option>
                                                    <option value="dropped" {{ request('enrollment_status') == 'dropped' ? 'selected' : '' }}>Dropped</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <label for="has_kmlttb" class="form-label">KMLTTB Status</label>
                                                <select class="form-select" id="has_kmlttb" name="has_kmlttb">
                                                    <option value="">All Students</option>
                                                    <option value="yes" {{ request('has_kmlttb') == 'yes' ? 'selected' : '' }}>Has KMLTTB No</option>
                                                    <option value="no" {{ request('has_kmlttb') == 'no' ? 'selected' : '' }}>No KMLTTB No</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <label for="min_progress" class="form-label">Min Progress %</label>
                                                <input type="number" class="form-control" id="min_progress" name="min_progress"
                                                       min="0" max="100" placeholder="0" value="{{ request('min_progress') }}">
                                            </div>
                                            <div class="col-md-2">
                                                <label for="max_progress" class="form-label">Max Progress %</label>
                                                <input type="number" class="form-control" id="max_progress" name="max_progress"
                                                       min="0" max="100" placeholder="100" value="{{ request('max_progress') }}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Registration Reports Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div>
                                    <h4 class="card-title">Course Registration Summary</h4>
                                    <p class="card-title-desc">Overview of course enrollments and revenue with KMLTTB compliance tracking</p>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">Total Courses: {{ $courses->total() }}</small>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-nowrap align-middle table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Course Details</th>
                                            <th>Enrollments</th>
                                            <th>Registration Period</th>
                                            <th>Revenue (KES)</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($courses as $course)
                                        <tr>
                                            <td>
                                                <div>
                                                    <h6 class="mb-1">{{ $course->title }}</h6>
                                                    <p class="text-muted font-size-13 mb-0">
                                                        ID: {{ $course->id }} | 
                                                        Price: KES {{ number_format($course->price, 0) }}
                                                    </p>
                                                    @if($course->has_live_session)
                                                        <span class="badge badge-soft-info font-size-11">Live Session</span>
                                                    @endif
                                                    @if($course->certificate)
                                                        <span class="badge badge-soft-success font-size-11">KMLTTB Certified</span>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-xs me-3">
                                                        <span class="avatar-title rounded-circle bg-primary text-white font-size-12">
                                                            {{ $course->enrollments_count }}
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">{{ $course->enrollments_count }} Students</h6>
                                                        <p class="text-muted font-size-13 mb-0">Enrolled</p>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                @if($course->earliest_enrollment && $course->latest_enrollment)
                                                    <div>
                                                        <p class="mb-1 font-size-13">
                                                            <strong>First:</strong> {{ \Carbon\Carbon::parse($course->earliest_enrollment)->format('M d, Y') }}
                                                        </p>
                                                        <p class="mb-0 font-size-13 text-muted">
                                                            <strong>Latest:</strong> {{ \Carbon\Carbon::parse($course->latest_enrollment)->format('M d, Y') }}
                                                        </p>
                                                    </div>
                                                @else
                                                    <span class="text-muted">No enrollments</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div>
                                                    <h6 class="mb-0 text-success">KES {{ number_format($course->total_revenue ?? 0, 0) }}</h6>
                                                    <p class="text-muted font-size-13 mb-0">
                                                        Avg: KES {{ $course->enrollments_count > 0 ? number_format(($course->total_revenue ?? 0) / $course->enrollments_count, 0) : 0 }}
                                                    </p>
                                                </div>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-outline-primary btn-sm" 
                                                        onclick="viewCourseUsers({{ $course->id }}, '{{ $course->title }}')"
                                                        {{ $course->enrollments_count == 0 ? 'disabled' : '' }}>
                                                    <i class="uil-users-alt me-1"></i> View Users
                                                </button>
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <div class="text-muted">
                                                    <i class="uil-graduation-cap font-size-24 mb-2 d-block"></i>
                                                    <p class="mb-0">No course registration data found</p>
                                                    <small>Try adjusting your filters or check back later</small>
                                                </div>
                                            </td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            @if($courses->hasPages())
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="pagination-wrap hstack gap-2 justify-content-center">
                                            {{ $courses->appends(request()->query())->links() }}
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<!-- User Details Modal -->
<div class="modal fade" id="userDetailsModal" tabindex="-1" aria-labelledby="userDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userDetailsModalLabel">Course Enrollment Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="userDetailsContent">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading user details...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="exportCourseUsers()">
                    <i class="uil-export me-1"></i> Export Users
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let currentCourseId = null;

function applyDatePreset() {
    const preset = document.getElementById('date_preset').value;
    const dateFrom = document.getElementById('date_from');
    const dateTo = document.getElementById('date_to');

    if (!preset) return;

    const today = new Date();
    const formatDate = (date) => date.toISOString().split('T')[0];

    switch(preset) {
        case 'today':
            dateFrom.value = formatDate(today);
            dateTo.value = formatDate(today);
            break;
        case 'yesterday':
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            dateFrom.value = formatDate(yesterday);
            dateTo.value = formatDate(yesterday);
            break;
        case 'last_7_days':
            const last7Days = new Date(today);
            last7Days.setDate(last7Days.getDate() - 7);
            dateFrom.value = formatDate(last7Days);
            dateTo.value = formatDate(today);
            break;
        case 'last_30_days':
            const last30Days = new Date(today);
            last30Days.setDate(last30Days.getDate() - 30);
            dateFrom.value = formatDate(last30Days);
            dateTo.value = formatDate(today);
            break;
        case 'this_month':
            const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
            dateFrom.value = formatDate(thisMonthStart);
            dateTo.value = formatDate(today);
            break;
        case 'last_month':
            const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
            const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
            dateFrom.value = formatDate(lastMonthStart);
            dateTo.value = formatDate(lastMonthEnd);
            break;
        case 'this_quarter':
            const quarterStart = new Date(today.getFullYear(), Math.floor(today.getMonth() / 3) * 3, 1);
            dateFrom.value = formatDate(quarterStart);
            dateTo.value = formatDate(today);
            break;
        case 'this_year':
            const yearStart = new Date(today.getFullYear(), 0, 1);
            dateFrom.value = formatDate(yearStart);
            dateTo.value = formatDate(today);
            break;
    }
}

function viewCourseUsers(courseId, courseTitle) {
    currentCourseId = courseId;
    $('#userDetailsModalLabel').text('Enrollment Details: ' + courseTitle);
    $('#userDetailsModal').modal('show');
    
    // Show loading state
    $('#userDetailsContent').html(`
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading user details...</p>
        </div>
    `);
    
    // Fetch user details
    fetch(`{{ url('admin/reports/course') }}/${courseId}/users`)
        .then(response => response.json())
        .then(data => {
            displayUserDetails(data.users);
        })
        .catch(error => {
            console.error('Error:', error);
            $('#userDetailsContent').html(`
                <div class="alert alert-danger">
                    <i class="uil-exclamation-triangle me-2"></i>
                    Error loading user details. Please try again.
                </div>
            `);
        });
}

function displayUserDetails(users) {
    if (users.length === 0) {
        $('#userDetailsContent').html(`
            <div class="text-center text-muted">
                <i class="uil-users-alt font-size-24 mb-2 d-block"></i>
                <p>No enrolled users found for this course.</p>
            </div>
        `);
        return;
    }
    
    let html = `
        <div class="table-responsive">
            <table class="table table-nowrap align-middle table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Student Details</th>
                        <th>KMLTTB Info</th>
                        <th>Enrollment</th>
                        <th>Progress</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    users.forEach(user => {
        html += `
            <tr>
                <td>
                    <div>
                        <h6 class="mb-1">${user.name}</h6>
                        <p class="text-muted font-size-13 mb-1">${user.email}</p>
                        <p class="text-muted font-size-13 mb-0">Phone: ${user.phone}</p>
                    </div>
                </td>
                <td>
                    <div>
                        <p class="mb-1 font-size-13"><strong>ID No:</strong> ${user.id_no}</p>
                        <p class="mb-0 font-size-13"><strong>KMLTTB No:</strong> ${user.kmlttb_no}</p>
                    </div>
                </td>
                <td>
                    <div>
                        <p class="mb-1 font-size-13">${new Date(user.enrolled_at).toLocaleDateString()}</p>
                        <span class="badge badge-soft-${getStatusColor(user.status)}">${user.status}</span>
                    </div>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="progress progress-sm">
                                <div class="progress-bar bg-primary" role="progressbar" 
                                     style="width: ${user.progress}%" 
                                     aria-valuenow="${user.progress}" 
                                     aria-valuemin="0" 
                                     aria-valuemax="100"></div>
                            </div>
                        </div>
                        <div class="flex-shrink-0 ms-2">
                            <small class="text-muted">${user.progress}%</small>
                        </div>
                    </div>
                </td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
        <div class="mt-3">
            <div class="row">
                <div class="col-md-4">
                    <div class="text-center">
                        <h5 class="text-primary">${users.length}</h5>
                        <p class="text-muted mb-0">Total Students</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <h5 class="text-success">${users.filter(u => u.status === 'completed').length}</h5>
                        <p class="text-muted mb-0">Completed</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <h5 class="text-warning">${users.filter(u => u.status === 'in_progress').length}</h5>
                        <p class="text-muted mb-0">In Progress</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    $('#userDetailsContent').html(html);
}

function getStatusColor(status) {
    switch(status) {
        case 'completed': return 'success';
        case 'in_progress': return 'warning';
        case 'enrolled': return 'info';
        default: return 'secondary';
    }
}

function exportData() {
    // Get current filter parameters
    const params = new URLSearchParams();
    const courseId = document.getElementById('course_id').value;
    const dateFrom = document.getElementById('date_from').value;
    const dateTo = document.getElementById('date_to').value;

    if (courseId) params.append('course_id', courseId);
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);

    // Create download link
    const exportUrl = `{{ route('admin.reports.export.registrations') }}?${params.toString()}`;
    window.open(exportUrl, '_blank');
}

function exportCourseUsers() {
    if (currentCourseId) {
        // Export specific course users
        const exportUrl = `{{ route('admin.reports.export.registrations') }}?course_id=${currentCourseId}`;
        window.open(exportUrl, '_blank');
    }
}
</script>
@endpush
