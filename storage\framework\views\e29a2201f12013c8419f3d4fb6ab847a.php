<?php $__env->startSection('content'); ?>
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">

            <!-- start page title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-flex align-items-center justify-content-between">
                        <h4 class="mb-0">Meeting Attendance Compliance Reports</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                                <li class="breadcrumb-item"><a href="<?php echo e(route('admin.reports.index')); ?>">Reports</a></li>
                                <li class="breadcrumb-item active">Attendance</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end page title -->

            <!-- Filters Card -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title mb-4">Filter Attendance Reports</h4>
                            <form method="GET" action="<?php echo e(route('admin.reports.attendance')); ?>" class="row g-3">
                                <div class="col-md-3">
                                    <label for="course_id" class="form-label">Course</label>
                                    <select class="form-select" id="course_id" name="course_id">
                                        <option value="">All Courses</option>
                                        <?php $__currentLoopData = $allCourses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($course->id); ?>" <?php echo e(request('course_id') == $course->id ? 'selected' : ''); ?>>
                                                <?php echo e($course->title); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="date_from" class="form-label">From Date</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo e(request('date_from')); ?>">
                                </div>
                                <div class="col-md-2">
                                    <label for="date_to" class="form-label">To Date</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo e(request('date_to')); ?>">
                                </div>
                                <div class="col-md-2">
                                    <label for="compliance_status" class="form-label">Compliance</label>
                                    <select class="form-select" id="compliance_status" name="compliance_status">
                                        <option value="">All Status</option>
                                        <option value="compliant" <?php echo e(request('compliance_status') == 'compliant' ? 'selected' : ''); ?>>Compliant</option>
                                        <option value="non_compliant" <?php echo e(request('compliance_status') == 'non_compliant' ? 'selected' : ''); ?>>Non-Compliant</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid gap-2 d-md-flex">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="uil-filter me-1"></i> Apply
                                        </button>
                                        <a href="<?php echo e(route('admin.reports.attendance')); ?>" class="btn btn-outline-secondary">
                                            <i class="uil-refresh me-1"></i> Reset
                                        </a>
                                        <button type="button" class="btn btn-success" onclick="exportAttendanceData()">
                                            <i class="uil-export me-1"></i> Export
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Compliance Overview -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div>
                                    <h4 class="card-title">Meeting Compliance Overview</h4>
                                    <p class="card-title-desc">KMLTTB 45-minute minimum attendance requirement tracking</p>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">Total Meetings: <?php echo e($meetings->total()); ?></small>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-nowrap align-middle table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Meeting Details</th>
                                            <th>Attendance Summary</th>
                                            <th>Compliance Rate</th>
                                            <th>Average Duration</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__empty_1 = true; $__currentLoopData = $meetings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $meeting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <h6 class="mb-1"><?php echo e($meeting->topic); ?></h6>
                                                    <p class="text-muted font-size-13 mb-1">
                                                        <strong>Course:</strong> <?php echo e($meeting->course->title); ?>

                                                    </p>
                                                    <p class="text-muted font-size-13 mb-0">
                                                        <i class="uil-calendar-alt me-1"></i>
                                                        <?php echo e($meeting->start_time->format('M d, Y H:i')); ?>

                                                    </p>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-3">
                                                        <div class="avatar-sm">
                                                            <span class="avatar-title rounded-circle bg-primary text-white font-size-12">
                                                                <?php echo e($meeting->total_attendees); ?>

                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <p class="mb-1 font-size-13">
                                                            <strong><?php echo e($meeting->total_attendees); ?></strong> attended / 
                                                            <strong><?php echo e($meeting->total_registered); ?></strong> registered
                                                        </p>
                                                        <p class="text-muted font-size-13 mb-0">
                                                            <?php echo e($meeting->compliant_attendees); ?> met 45-min requirement
                                                        </p>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-grow-1">
                                                        <div class="progress progress-sm">
                                                            <div class="progress-bar <?php echo e($meeting->compliance_rate >= 80 ? 'bg-success' : ($meeting->compliance_rate >= 60 ? 'bg-warning' : 'bg-danger')); ?>" 
                                                                 role="progressbar" 
                                                                 style="width: <?php echo e($meeting->compliance_rate); ?>%" 
                                                                 aria-valuenow="<?php echo e($meeting->compliance_rate); ?>" 
                                                                 aria-valuemin="0" 
                                                                 aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="flex-shrink-0 ms-2">
                                                        <span class="badge badge-soft-<?php echo e($meeting->compliance_rate >= 80 ? 'success' : ($meeting->compliance_rate >= 60 ? 'warning' : 'danger')); ?>">
                                                            <?php echo e(number_format($meeting->compliance_rate, 1)); ?>%
                                                        </span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <h6 class="mb-0"><?php echo e(number_format($meeting->average_duration, 0)); ?> min</h6>
                                                    <p class="text-muted font-size-13 mb-0">
                                                        <?php echo e($meeting->average_duration >= 45 ? 'Above' : 'Below'); ?> minimum
                                                    </p>
                                                </div>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-outline-primary btn-sm" 
                                                        onclick="viewMeetingAttendance(<?php echo e($meeting->id); ?>, '<?php echo e($meeting->topic); ?>')"
                                                        <?php echo e($meeting->total_attendees == 0 ? 'disabled' : ''); ?>>
                                                    <i class="uil-eye me-1"></i> View Details
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <div class="text-muted">
                                                    <i class="uil-video font-size-24 mb-2 d-block"></i>
                                                    <p class="mb-0">No meeting attendance data found</p>
                                                    <small>Try adjusting your filters or check back later</small>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php if($meetings->hasPages()): ?>
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="pagination-wrap hstack gap-2 justify-content-center">
                                            <?php echo e($meetings->appends(request()->query())->links()); ?>

                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Non-Compliance Tracking -->
            <?php if($nonCompliantAttendances->count() > 0): ?>
            <div class="row">
                <div class="col-12">
                    <div class="card border-warning">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div>
                                    <h4 class="card-title text-warning">
                                        <i class="uil-exclamation-triangle me-2"></i>Non-Compliance Alert
                                    </h4>
                                    <p class="card-title-desc">Students who did not meet the 45-minute KMLTTB requirement</p>
                                </div>
                                <div>
                                    <span class="badge badge-soft-warning font-size-14"><?php echo e($nonCompliantAttendances->count()); ?> Records</span>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-nowrap align-middle table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Student Details</th>
                                            <th>KMLTTB Info</th>
                                            <th>Meeting</th>
                                            <th>Attendance Duration</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $nonCompliantAttendances; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attendance): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <h6 class="mb-1"><?php echo e($attendance->user->name); ?></h6>
                                                    <p class="text-muted font-size-13 mb-0"><?php echo e($attendance->user->email); ?></p>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <p class="mb-1 font-size-13"><strong>ID No:</strong> <?php echo e($attendance->user->id_no ?? 'N/A'); ?></p>
                                                    <p class="mb-0 font-size-13"><strong>KMLTTB No:</strong> <?php echo e($attendance->user->kmlttb_no ?? 'N/A'); ?></p>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <h6 class="mb-1"><?php echo e($attendance->zoomMeeting->topic); ?></h6>
                                                    <p class="text-muted font-size-13 mb-0">
                                                        <?php echo e($attendance->zoomMeeting->course->title); ?>

                                                    </p>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-2">
                                                        <span class="badge badge-soft-danger"><?php echo e($attendance->duration_minutes); ?> min</span>
                                                    </div>
                                                    <div>
                                                        <small class="text-muted">
                                                            Need: <?php echo e(45 - $attendance->duration_minutes); ?> more min
                                                        </small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge badge-soft-danger">Non-Compliant</span>
                                                <br>
                                                <small class="text-muted">No CPD Credit</small>
                                            </td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

        </div>
    </div>
</div>

<!-- Meeting Attendance Details Modal -->
<div class="modal fade" id="attendanceDetailsModal" tabindex="-1" aria-labelledby="attendanceDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="attendanceDetailsModalLabel">Meeting Attendance Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="attendanceDetailsContent">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading attendance details...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="exportMeetingAttendance()">
                    <i class="uil-export me-1"></i> Export Attendance
                </button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
let currentMeetingId = null;

function viewMeetingAttendance(meetingId, meetingTopic) {
    currentMeetingId = meetingId;
    $('#attendanceDetailsModalLabel').text('Attendance Details: ' + meetingTopic);
    $('#attendanceDetailsModal').modal('show');

    // Show loading state
    $('#attendanceDetailsContent').html(`
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading attendance details...</p>
        </div>
    `);

    // Fetch attendance details
    fetch(`<?php echo e(url('admin/reports/meeting')); ?>/${meetingId}/attendance`)
        .then(response => response.json())
        .then(data => {
            displayAttendanceDetails(data.attendances);
        })
        .catch(error => {
            console.error('Error:', error);
            $('#attendanceDetailsContent').html(`
                <div class="alert alert-danger">
                    <i class="uil-exclamation-triangle me-2"></i>
                    Error loading attendance details. Please try again.
                </div>
            `);
        });
}

function displayAttendanceDetails(attendances) {
    if (attendances.length === 0) {
        $('#attendanceDetailsContent').html(`
            <div class="text-center text-muted">
                <i class="uil-users-alt font-size-24 mb-2 d-block"></i>
                <p>No attendance records found for this meeting.</p>
            </div>
        `);
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-nowrap align-middle table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Participant Details</th>
                        <th>KMLTTB Information</th>
                        <th>Join/Leave Times</th>
                        <th>Duration & Compliance</th>
                        <th>CPD Status</th>
                    </tr>
                </thead>
                <tbody>
    `;

    attendances.forEach(attendance => {
        const isCompliant = attendance.duration_minutes >= 45;
        const complianceClass = isCompliant ? 'success' : 'danger';
        const cpd_hours = isCompliant ? (attendance.duration_minutes / 60).toFixed(1) : '0';

        html += `
            <tr>
                <td>
                    <div>
                        <h6 class="mb-1">${attendance.user_name}</h6>
                        <p class="text-muted font-size-13 mb-0">${attendance.user_email}</p>
                    </div>
                </td>
                <td>
                    <div>
                        <p class="mb-1 font-size-13"><strong>ID No:</strong> ${attendance.id_no}</p>
                        <p class="mb-0 font-size-13"><strong>KMLTTB No:</strong> ${attendance.kmlttb_no}</p>
                    </div>
                </td>
                <td>
                    <div>
                        <p class="mb-1 font-size-13">
                            <i class="uil-signin text-success me-1"></i>
                            <strong>Joined:</strong> ${attendance.joined_at !== 'N/A' ? new Date(attendance.joined_at).toLocaleString() : 'N/A'}
                        </p>
                        <p class="mb-0 font-size-13">
                            <i class="uil-signout text-danger me-1"></i>
                            <strong>Left:</strong> ${attendance.left_at !== 'N/A' ? new Date(attendance.left_at).toLocaleString() : 'N/A'}
                        </p>
                    </div>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="badge badge-soft-${complianceClass} font-size-12">
                                ${attendance.duration_minutes} min
                            </span>
                        </div>
                        <div>
                            <p class="mb-0 font-size-13">
                                <span class="badge badge-soft-${complianceClass}">
                                    ${attendance.compliance_status}
                                </span>
                            </p>
                        </div>
                    </div>
                </td>
                <td>
                    <div>
                        <p class="mb-1 font-size-13">
                            <strong>CPD Hours:</strong> ${cpd_hours}
                        </p>
                        <p class="mb-0 font-size-13">
                            <span class="badge badge-soft-${isCompliant ? 'success' : 'warning'}">
                                ${isCompliant ? 'Eligible' : 'Not Eligible'}
                            </span>
                        </p>
                    </div>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
        <div class="mt-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h5 class="text-primary">${attendances.length}</h5>
                        <p class="text-muted mb-0">Total Attendees</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5 class="text-success">${attendances.filter(a => a.duration_minutes >= 45).length}</h5>
                        <p class="text-muted mb-0">KMLTTB Compliant</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5 class="text-warning">${attendances.filter(a => a.duration_minutes < 45).length}</h5>
                        <p class="text-muted mb-0">Non-Compliant</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5 class="text-info">${(attendances.reduce((sum, a) => sum + (a.duration_minutes >= 45 ? a.duration_minutes / 60 : 0), 0)).toFixed(1)}</h5>
                        <p class="text-muted mb-0">Total CPD Hours</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    $('#attendanceDetailsContent').html(html);
}

function exportAttendanceData() {
    // Get current filter parameters
    const params = new URLSearchParams();
    const courseId = document.getElementById('course_id').value;
    const dateFrom = document.getElementById('date_from').value;
    const dateTo = document.getElementById('date_to').value;
    const complianceStatus = document.getElementById('compliance_status').value;

    if (courseId) params.append('course_id', courseId);
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);
    if (complianceStatus) params.append('compliance_status', complianceStatus);

    // Create download link
    const exportUrl = `<?php echo e(route('admin.reports.export.attendance')); ?>?${params.toString()}`;
    window.open(exportUrl, '_blank');
}

function exportMeetingAttendance() {
    if (currentMeetingId) {
        // Export specific meeting attendance
        const exportUrl = `<?php echo e(route('admin.reports.export.attendance')); ?>?meeting_id=${currentMeetingId}`;
        window.open(exportUrl, '_blank');
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Laravel-Apps\lernovate\resources\views/admin/reports/attendance.blade.php ENDPATH**/ ?>