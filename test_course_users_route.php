<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Models\Course;
use App\Models\User;
use App\Models\Enrollment;
use App\Http\Controllers\Admin\ReportsController;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Testing Course Users Route Fix ===\n";

try {
    // Find a course with enrollments or create test data
    $course = Course::with(['enrollments.user'])->first();
    
    if (!$course) {
        echo "No courses found in database. Creating test data...\n";
        
        // This would require creating test data, but for now we'll just report
        echo "⚠ No courses found. Please ensure there are courses in the database.\n";
        exit(1);
    }
    
    echo "Testing with course: {$course->title} (ID: {$course->id})\n";
    echo "Course has {$course->enrollments->count()} enrollments\n";
    
    // Test the controller method directly
    $controller = new ReportsController();
    $request = new Request();
    
    echo "\nCalling getCourseUsers method...\n";
    $response = $controller->getCourseUsers($request, $course);
    
    if ($response instanceof \Illuminate\Http\JsonResponse) {
        $data = $response->getData(true);
        echo "✓ Route method executed successfully\n";
        echo "✓ Response is JSON\n";
        echo "✓ Users count: " . count($data['users']) . "\n";
        
        if (count($data['users']) > 0) {
            echo "✓ Sample user data:\n";
            $sample = $data['users'][0];
            echo "  - Name: {$sample['name']}\n";
            echo "  - Email: {$sample['email']}\n";
            echo "  - ID No: {$sample['id_no']}\n";
            echo "  - KMLTTB No: {$sample['kmlttb_no']}\n";
            echo "  - Enrolled: {$sample['enrolled_at']}\n";
            echo "  - Status: {$sample['status']}\n";
            echo "  - Progress: {$sample['progress']}%\n";
        }
    } else {
        echo "✗ Unexpected response type\n";
        var_dump($response);
    }
    
    // Test route generation
    echo "\nTesting route generation...\n";
    $routeUrl = route('admin.reports.course.users', $course->id);
    echo "✓ Route URL: {$routeUrl}\n";
    
    // Test URL construction (as used in JavaScript)
    $baseUrl = url('admin/reports/course');
    $constructedUrl = "{$baseUrl}/{$course->id}/users";
    echo "✓ Constructed URL: {$constructedUrl}\n";
    
    if ($routeUrl === $constructedUrl) {
        echo "✓ Route URLs match perfectly\n";
    } else {
        echo "⚠ Route URLs differ:\n";
        echo "  Route helper: {$routeUrl}\n";
        echo "  Constructed:  {$constructedUrl}\n";
    }
    
    // Test with different course IDs to ensure route model binding works
    echo "\nTesting route model binding...\n";
    $allCourses = Course::limit(3)->get();
    foreach ($allCourses as $testCourse) {
        try {
            $testResponse = $controller->getCourseUsers($request, $testCourse);
            if ($testResponse instanceof \Illuminate\Http\JsonResponse) {
                $testData = $testResponse->getData(true);
                echo "✓ Course '{$testCourse->title}' - {$testCourse->enrollments->count()} enrollments, {$testData['users']->count()} users returned\n";
            }
        } catch (Exception $e) {
            echo "✗ Error with course {$testCourse->id}: {$e->getMessage()}\n";
        }
    }
    
    echo "\n✓ All tests passed! The course users route should work correctly.\n";
    echo "✓ JavaScript AJAX calls will now succeed\n";
    echo "✓ Admin can view detailed user enrollment information\n";
    echo "✓ KMLTTB compliance data (ID No, KMLTTB No) is properly included\n";
    
} catch (Exception $e) {
    echo "\n✗ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== Test Complete ===\n";
