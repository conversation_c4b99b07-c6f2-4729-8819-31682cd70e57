<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\VisitorsController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\UserDashboardController;
use App\Http\Controllers\Admin\CMS\PageController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

Route::get('/', [HomeController::class, 'index'])->name('home');

// Generic Dashboard Route - Redirects to role-based dashboard
Route::get('/dashboard', function () {
    $user = Auth::user();
    if ($user->isAdmin()) {
        return redirect()->route('admin.dashboard');
    } else {
        return redirect()->route('user.dashboard');
    }
})->middleware(['auth', 'verified'])->name('dashboard');

// Admin Dashboard
Route::get('/admin-dashboard', [\App\Http\Controllers\Admin\AdminDashboardController::class, 'index'])
    ->middleware(['auth', 'verified', 'role:admin'])
    ->name('admin.dashboard');

// User Dashboard
Route::get('/user/dashboard', [\App\Http\Controllers\UserDashboardController::class, 'dashboard'])
    ->middleware(['auth', 'verified', 'role:user'])
    ->name('user.dashboard');

// User Dashboard Routes
Route::middleware(['auth', 'verified', 'role:user'])->prefix('user')->name('user.')->group(function () {
    // My Courses
    Route::get('/courses/enrolled', [\App\Http\Controllers\UserDashboardController::class, 'enrolledCourses'])->name('courses.enrolled');
    Route::get('/courses/completed', [\App\Http\Controllers\UserDashboardController::class, 'completedCourses'])->name('courses.completed');
    Route::get('/courses/progress', [\App\Http\Controllers\UserDashboardController::class, 'courseProgress'])->name('courses.progress');
    
    // Diagnostic Courses
    Route::get('/diagnostic-courses/{category?}', [\App\Http\Controllers\UserDashboardController::class, 'diagnosticCourses'])->name('diagnostic.courses');
    
    // Certificates
    Route::get('/certificates', [\App\Http\Controllers\UserDashboardController::class, 'certificates'])->name('certificates');
    
    // Assessments
    Route::get('/assessments', [\App\Http\Controllers\UserDashboardController::class, 'assessments'])->name('assessments');

    // Meeting Routes
    Route::get('/meetings', [\App\Http\Controllers\MeetingController::class, 'index'])->name('meetings.index');
    Route::get('/meetings/{meeting}', [\App\Http\Controllers\MeetingController::class, 'show'])->name('meetings.show');
    Route::get('/meetings/{meeting}/join', [\App\Http\Controllers\MeetingController::class, 'join'])->name('meetings.join');
    Route::get('/meetings/{meeting}/recordings', [\App\Http\Controllers\MeetingController::class, 'recordings'])->name('meetings.recordings');
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Admin Category Management Routes
Route::middleware(['auth', 'verified', 'role:admin'])->group(function () {
    Route::get('/add-category', [VisitorsController::class, 'add_category'])->name('add.category');
    Route::post('/add-category', [VisitorsController::class, 'store_category'])->name('store.category');
    Route::get('/all-categories', [VisitorsController::class, 'all_categories'])->name('all.categories');
});

require __DIR__.'/auth.php';

Route::controller(VisitorsController::class)->group(function(){
    Route::get('/courses', 'courses')->name('visitors.courses');
    Route::get('/fetch-courses', 'fetchCourses')->name('visitors.fetch-courses');
    Route::get('/course/{id}', 'course')->name('visitors.course');

    Route::get('/about', 'about')->name('visitors.about');
    Route::get('/cpd-programs', 'cpdPrograms')->name('visitors.cpd-programs');
    Route::get('/certification', 'certification')->name('visitors.certification');
    Route::get('/contact', 'contact')->name('visitors.contact');
    Route::post('/contact', 'contactSubmit')->name('visitors.contact.submit');
    Route::get('/page/terms-and-conditions', 'termsAndConditions')->name('page.terms-and-conditions');
    Route::get('/logout', 'destroy')->name('visitors.logout');
    Route::post('/payment', 'payCourses')->name('checkout');
});

// Peter test route for course data display (must come before dynamic route)
Route::get('/course/peter-test', [HomeController::class, 'peterTest'])->name('course.peter-test');

// Course detail route
Route::get('/course/{slug}', [HomeController::class, 'show'])->name('course.show');

// Cart Routes
Route::controller(CartController::class)->prefix('cart')->name('cart.')->group(function () {
    // Cart page
    Route::get('/', 'index')->name('index');

    // AJAX cart operations
    Route::post('/add/{courseId}', 'add')->name('add');
    Route::delete('/remove/{courseId}', 'remove')->name('remove');
    Route::patch('/update/{courseId}', 'updateQuantity')->name('update');
    Route::delete('/clear', 'clear')->name('clear');

    // Cart data endpoints
    Route::get('/get', 'getCart')->name('get');
    Route::get('/count', 'getCount')->name('count');
    Route::get('/validate', 'validate')->name('validate');
    Route::get('/modal', 'getModal')->name('modal');
});

// Legacy cart route for existing JavaScript compatibility
Route::post('/add-to-cart/{courseId}', [CartController::class, 'add'])->name('add-to-cart');

// Checkout Routes
Route::controller(\App\Http\Controllers\CheckoutController::class)->prefix('checkout')->name('checkout.')->group(function () {
    // Checkout page (requires authentication)
    Route::get('/', 'index')->name('index')->middleware('auth');

    // Process checkout (requires authentication)
    Route::post('/process', 'process')->name('process')->middleware('auth');

    // Payment status check
    Route::post('/status', 'checkPaymentStatus')->name('status')->middleware('auth');

    // Success and failed pages
    Route::get('/success/{orderNumber}', 'success')->name('success')->middleware('auth');
    Route::get('/failed', 'failed')->name('failed');

    // M-Pesa callback (no auth required for external callbacks)
    Route::post('/mpesa/callback', 'mpesaCallback')->name('mpesa.callback');
});

// Debug route to test cart functionality
Route::get('/test-cart', function () {
    $courses = \App\Models\Course::where('status', 'published')->where('is_free', false)->take(3)->get();
    return response()->json([
        'courses_count' => $courses->count(),
        'courses' => $courses->map(function($course) {
            return [
                'id' => $course->id,
                'title' => $course->title,
                'price' => $course->price,
                'status' => $course->status,
                'is_free' => $course->is_free
            ];
        })
    ]);
})->name('test-cart');

// Admin Courses Routes
Route::middleware(['auth', 'verified', 'role:admin'])->group(function () {
    // Courses Management
    Route::get('/admin-courses', [\App\Http\Controllers\Admin\CourseController::class, 'index'])->name('admin.courses.index');
    Route::get('/admin-courses-create', [\App\Http\Controllers\Admin\CourseController::class, 'create'])->name('admin.courses.create');
    Route::get('/admin-courses-debug', [\App\Http\Controllers\Admin\CourseController::class, 'debug'])->name('admin.courses.debug');
    Route::post('/admin-courses-store', [\App\Http\Controllers\Admin\CourseController::class, 'store'])->name('admin.courses.store');
    Route::get('/admin-courses-show-{id}', [\App\Http\Controllers\Admin\CourseController::class, 'show'])->name('admin.courses.show');
    Route::get('/admin-courses-edit-{id}', [\App\Http\Controllers\Admin\CourseController::class, 'edit'])->name('admin.courses.edit');
    Route::put('/admin-courses-update-{id}', [\App\Http\Controllers\Admin\CourseController::class, 'update'])->name('admin.courses.update');
    Route::delete('/admin-courses-delete-{id}', [\App\Http\Controllers\Admin\CourseController::class, 'destroy'])->name('admin.courses.destroy');
    Route::post('/admin-courses-bulk-action', [\App\Http\Controllers\Admin\CourseController::class, 'bulkAction'])->name('admin.courses.bulk-action');

    // AJAX endpoints for inline creation
    Route::post('/admin/courses/ajax/create-category', [\App\Http\Controllers\Admin\CourseController::class, 'createCategoryAjax'])->name('admin.courses.ajax.create-category');
    Route::post('/admin/courses/ajax/create-instructor', [\App\Http\Controllers\Admin\CourseController::class, 'createInstructorAjax'])->name('admin.courses.ajax.create-instructor');

    // Admin Meeting Management
    Route::get('/admin/meetings', [\App\Http\Controllers\MeetingController::class, 'adminIndex'])->name('admin.meetings.index');
    Route::get('/admin/meetings/{meeting}', [\App\Http\Controllers\MeetingController::class, 'adminShow'])->name('admin.meetings.show');
    Route::post('/admin/meetings/{meeting}/sync-attendance', [\App\Http\Controllers\MeetingController::class, 'syncAttendance'])->name('admin.meetings.sync-attendance');
    Route::post('/admin/meetings/{meeting}/update-status', [\App\Http\Controllers\MeetingController::class, 'updateStatus'])->name('admin.meetings.update-status');
    Route::post('/admin/meetings/{meeting}/update-recording', [\App\Http\Controllers\MeetingController::class, 'updateRecording'])->name('admin.meetings.update-recording');
});

// Admin Landing Page Management Routes
Route::middleware(['auth', 'verified', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::controller(\App\Http\Controllers\Admin\CMS\PageController::class)->prefix('landing-page')->name('landing.')->group(function () {
        Route::get('/', 'index')->name('index');
        Route::get('/create', 'create')->name('create');
        Route::post('/store', 'store')->name('store');
        Route::get('/{page}/edit', 'edit')->name('edit');
        Route::put('/{page}/update', 'update')->name('update');
        Route::delete('/{page}/destroy', 'destroy')->name('destroy');
        Route::get('/homepage', 'homepage')->name('homepage');
        Route::post('/homepage/update', 'updateHomepage')->name('homepage.update');
    });

    // Homepage Content Management
    Route::controller(\App\Http\Controllers\Admin\HomepageController::class)->prefix('homepage')->name('homepage.')->group(function () {
        Route::get('/', 'index')->name('index');
        Route::put('/update', 'update')->name('update');
        Route::post('/add-custom-section', 'addCustomSection')->name('addCustomSection');
        Route::post('/delete-custom-section/{key}', 'deleteCustomSection')->name('deleteCustomSection');
        Route::get('/seed-default-settings', 'seedDefaultSettings')->name('seedDefaultSettings');
    });

    // Admin Reports Routes
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\ReportsController::class, 'index'])->name('index');
        Route::get('/registrations', [\App\Http\Controllers\Admin\ReportsController::class, 'registrations'])->name('registrations');
        Route::get('/attendance', [\App\Http\Controllers\Admin\ReportsController::class, 'attendance'])->name('attendance');
        Route::get('/export/registrations', [\App\Http\Controllers\Admin\ReportsController::class, 'exportRegistrations'])->name('export.registrations');
        Route::get('/export/attendance', [\App\Http\Controllers\Admin\ReportsController::class, 'exportAttendance'])->name('export.attendance');
        Route::get('/course/{course}/users', [\App\Http\Controllers\Admin\ReportsController::class, 'getCourseUsers'])->name('course.users');
        Route::get('/meeting/{meeting}/attendance', [\App\Http\Controllers\Admin\ReportsController::class, 'getMeetingAttendance'])->name('meeting.attendance');
    });

    // Admin Order Management Routes
    Route::prefix('orders')->name('orders.')->group(function () {
        Route::get('/', function () {
            $orders = \App\Models\Order::with(['user', 'orderItems.course'])->latest()->paginate(20);
            return view('admin.orders.index', compact('orders'));
        })->name('index');

        Route::get('/{order}', function (\App\Models\Order $order) {
            $order->load(['user', 'orderItems.course']);
            return view('admin.orders.show', compact('order'));
        })->name('show');
    });
});

Route::post('/callback', [VisitorsController::class, 'callbackupdate'])->name('callback');

// Payment Status Routes
Route::middleware('auth')->group(function () {
    Route::get('/payment/status/{transaction_id}', [VisitorsController::class, 'paymentStatus'])->name('payment.status');
    Route::get('/payment/check/{transaction_id}', [VisitorsController::class, 'checkPaymentStatus'])->name('payment.check');
});

// Zoom Webhook Route (outside auth middleware)
Route::post('/zoom/webhook', [\App\Http\Controllers\MeetingController::class, 'webhook'])->name('zoom.webhook');



