<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\User;
use App\Models\Enrollment;
use App\Models\ZoomMeeting;
use App\Models\MeetingAttendance;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportsController extends Controller
{
    /**
     * Display the reports overview dashboard
     */
    public function index()
    {
        // Get key metrics for the overview dashboard
        $totalCourses = Course::where('status', 'published')->count();
        $totalEnrollments = Enrollment::count();
        $totalRevenue = Order::where('payment_status', 'paid')->sum('total');
        $totalMeetings = ZoomMeeting::count();
        
        // Recent enrollments (last 30 days)
        $recentEnrollments = Enrollment::where('enrolled_at', '>=', Carbon::now()->subDays(30))->count();
        
        // Attendance compliance rate
        $totalAttendances = MeetingAttendance::count();
        $compliantAttendances = MeetingAttendance::where('duration_minutes', '>=', 45)->count();
        $complianceRate = $totalAttendances > 0 ? round(($compliantAttendances / $totalAttendances) * 100, 2) : 0;
        
        // Top performing courses by enrollment
        $topCourses = Course::withCount('enrollments')
            ->where('status', 'published')
            ->orderBy('enrollments_count', 'desc')
            ->limit(5)
            ->get();
        
        // Monthly revenue trend (last 6 months)
        $monthlyRevenue = Order::where('payment_status', 'paid')
            ->where('created_at', '>=', Carbon::now()->subMonths(6))
            ->selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, SUM(total) as revenue')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();

        // KMLTTB compliance data
        $kmlttbCompliance = $this->getKmlttbComplianceSummary();
        $regulatoryIndicators = $this->getRegulatoryComplianceIndicators();

        return view('admin.reports.index', compact(
            'totalCourses',
            'totalEnrollments',
            'totalRevenue',
            'totalMeetings',
            'recentEnrollments',
            'complianceRate',
            'topCourses',
            'monthlyRevenue',
            'kmlttbCompliance',
            'regulatoryIndicators'
        ));
    }

    /**
     * Display course registration reports
     */
    public function registrations(Request $request)
    {
        $query = Course::with(['enrollments.user', 'enrollments.user.orders.orderItems'])
            ->where('status', 'published');

        // Apply filters
        if ($request->filled('course_id')) {
            $query->where('id', $request->course_id);
        }

        if ($request->filled('date_from')) {
            $query->whereHas('enrollments', function($q) use ($request) {
                $q->where('enrolled_at', '>=', Carbon::parse($request->date_from));
            });
        }

        if ($request->filled('date_to')) {
            $query->whereHas('enrollments', function($q) use ($request) {
                $q->where('enrolled_at', '<=', Carbon::parse($request->date_to));
            });
        }

        // Advanced filters
        if ($request->filled('search_term')) {
            $searchTerm = $request->search_term;
            $query->whereHas('enrollments.user', function($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('email', 'like', "%{$searchTerm}%")
                  ->orWhere('id_no', 'like', "%{$searchTerm}%")
                  ->orWhere('kmlttb_no', 'like', "%{$searchTerm}%");
            });
        }

        if ($request->filled('enrollment_status')) {
            $query->whereHas('enrollments', function($q) use ($request) {
                $q->where('status', $request->enrollment_status);
            });
        }

        if ($request->filled('has_kmlttb')) {
            if ($request->has_kmlttb === 'yes') {
                $query->whereHas('enrollments.user', function($q) {
                    $q->whereNotNull('kmlttb_no')->where('kmlttb_no', '!=', '');
                });
            } elseif ($request->has_kmlttb === 'no') {
                $query->whereHas('enrollments.user', function($q) {
                    $q->whereNull('kmlttb_no')->orWhere('kmlttb_no', '');
                });
            }
        }

        if ($request->filled('min_progress')) {
            $query->whereHas('enrollments', function($q) use ($request) {
                $q->where('progress', '>=', $request->min_progress);
            });
        }

        if ($request->filled('max_progress')) {
            $query->whereHas('enrollments', function($q) use ($request) {
                $q->where('progress', '<=', $request->max_progress);
            });
        }
        
        $courses = $query->withCount('enrollments')->paginate(15);
        
        // Calculate revenue for each course
        foreach ($courses as $course) {
            $course->total_revenue = OrderItem::whereHas('order', function($q) {
                    $q->where('payment_status', 'paid');
                })
                ->where('course_id', $course->id)
                ->sum(DB::raw('course_price * quantity'));
                
            // Get enrollment date range
            $enrollments = $course->enrollments;
            if ($enrollments->count() > 0) {
                $course->earliest_enrollment = $enrollments->min('enrolled_at');
                $course->latest_enrollment = $enrollments->max('enrolled_at');
            }
        }
        
        // Get all courses for filter dropdown
        $allCourses = Course::where('status', 'published')->orderBy('title')->get();
        
        return view('admin.reports.registrations', compact('courses', 'allCourses'));
    }

    /**
     * Display meeting attendance compliance reports
     */
    public function attendance(Request $request)
    {
        $query = ZoomMeeting::with(['course', 'attendances.user']);
        
        // Apply filters
        if ($request->filled('course_id')) {
            $query->where('course_id', $request->course_id);
        }
        
        if ($request->filled('date_from')) {
            $query->where('start_time', '>=', Carbon::parse($request->date_from));
        }
        
        if ($request->filled('date_to')) {
            $query->where('start_time', '<=', Carbon::parse($request->date_to));
        }
        
        if ($request->filled('compliance_status')) {
            if ($request->compliance_status === 'compliant') {
                $query->whereHas('attendances', function($q) {
                    $q->where('duration_minutes', '>=', 45);
                });
            } elseif ($request->compliance_status === 'non_compliant') {
                $query->whereHas('attendances', function($q) {
                    $q->where('duration_minutes', '<', 45);
                });
            }
        }
        
        $meetings = $query->orderBy('start_time', 'desc')->paginate(15);
        
        // Calculate compliance metrics for each meeting
        foreach ($meetings as $meeting) {
            $totalAttendees = $meeting->attendances->count();
            $compliantAttendees = $meeting->attendances->where('duration_minutes', '>=', 45)->count();
            
            $meeting->total_registered = $meeting->course->enrollments->count();
            $meeting->total_attendees = $totalAttendees;
            $meeting->compliant_attendees = $compliantAttendees;
            $meeting->compliance_rate = $totalAttendees > 0 ? round(($compliantAttendees / $totalAttendees) * 100, 2) : 0;
            $meeting->average_duration = $meeting->attendances->avg('duration_minutes') ?? 0;
        }
        
        // Get all courses for filter dropdown
        $allCourses = Course::where('status', 'published')->orderBy('title')->get();
        
        // Get non-compliant attendances for separate tracking
        $nonCompliantAttendances = MeetingAttendance::with(['user', 'zoomMeeting.course'])
            ->where('duration_minutes', '<', 45)
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();
        
        return view('admin.reports.attendance', compact('meetings', 'allCourses', 'nonCompliantAttendances'));
    }

    /**
     * Export registration data as CSV
     */
    public function exportRegistrations(Request $request)
    {
        $query = Course::with(['enrollments.user'])
            ->where('status', 'published');

        // Apply same filters as the main report
        if ($request->filled('course_id')) {
            $query->where('id', $request->course_id);
        }

        if ($request->filled('date_from')) {
            $query->whereHas('enrollments', function($q) use ($request) {
                $q->where('enrolled_at', '>=', Carbon::parse($request->date_from));
            });
        }

        if ($request->filled('date_to')) {
            $query->whereHas('enrollments', function($q) use ($request) {
                $q->where('enrolled_at', '<=', Carbon::parse($request->date_to));
            });
        }

        $courses = $query->get();

        $filename = 'course_registrations_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($courses) {
            $file = fopen('php://output', 'w');

            // CSV Headers
            fputcsv($file, [
                'Course ID',
                'Course Title',
                'Course Price (KES)',
                'Student Name',
                'Student Email',
                'Phone Number',
                'Identity Number (ID No)',
                'KMLTTB Number',
                'Enrollment Date',
                'Enrollment Status',
                'Progress (%)',
                'Has Live Session',
                'KMLTTB Certified'
            ]);

            foreach ($courses as $course) {
                foreach ($course->enrollments as $enrollment) {
                    fputcsv($file, [
                        $course->id,
                        $course->title,
                        number_format($course->price, 2),
                        $enrollment->user->name,
                        $enrollment->user->email,
                        $enrollment->user->phone ?? 'N/A',
                        $enrollment->user->id_no ?? 'N/A',
                        $enrollment->user->kmlttb_no ?? 'N/A',
                        $enrollment->enrolled_at->format('Y-m-d H:i:s'),
                        $enrollment->status,
                        $enrollment->progress,
                        $course->has_live_session ? 'Yes' : 'No',
                        $course->certificate ? 'Yes' : 'No'
                    ]);
                }
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export attendance data as CSV
     */
    public function exportAttendance(Request $request)
    {
        $query = ZoomMeeting::with(['course', 'attendances.user']);

        // Apply same filters as the main report
        if ($request->filled('course_id')) {
            $query->where('course_id', $request->course_id);
        }

        if ($request->filled('date_from')) {
            $query->where('start_time', '>=', Carbon::parse($request->date_from));
        }

        if ($request->filled('date_to')) {
            $query->where('start_time', '<=', Carbon::parse($request->date_to));
        }

        if ($request->filled('compliance_status')) {
            if ($request->compliance_status === 'compliant') {
                $query->whereHas('attendances', function($q) {
                    $q->where('duration_minutes', '>=', 45);
                });
            } elseif ($request->compliance_status === 'non_compliant') {
                $query->whereHas('attendances', function($q) {
                    $q->where('duration_minutes', '<', 45);
                });
            }
        }

        $meetings = $query->get();

        $filename = 'meeting_attendance_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($meetings) {
            $file = fopen('php://output', 'w');

            // CSV Headers
            fputcsv($file, [
                'Meeting ID',
                'Meeting Topic',
                'Course Title',
                'Meeting Date',
                'Student Name',
                'Student Email',
                'Identity Number (ID No)',
                'KMLTTB Number',
                'Joined At',
                'Left At',
                'Duration (Minutes)',
                'KMLTTB Compliant (45+ min)',
                'CPD Hours Earned',
                'Compliance Status'
            ]);

            foreach ($meetings as $meeting) {
                foreach ($meeting->attendances as $attendance) {
                    $isCompliant = $attendance->duration_minutes >= 45;
                    $cpdHours = $isCompliant ? round($attendance->duration_minutes / 60, 1) : 0;

                    fputcsv($file, [
                        $meeting->id,
                        $meeting->topic,
                        $meeting->course->title,
                        $meeting->start_time->format('Y-m-d H:i:s'),
                        $attendance->user->name,
                        $attendance->user->email,
                        $attendance->user->id_no ?? 'N/A',
                        $attendance->user->kmlttb_no ?? 'N/A',
                        $attendance->joined_at ? $attendance->joined_at->format('Y-m-d H:i:s') : 'N/A',
                        $attendance->left_at ? $attendance->left_at->format('Y-m-d H:i:s') : 'N/A',
                        $attendance->duration_minutes,
                        $isCompliant ? 'Yes' : 'No',
                        $cpdHours,
                        $isCompliant ? 'Compliant' : 'Non-Compliant'
                    ]);
                }
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get detailed user information for a specific course (AJAX)
     */
    public function getCourseUsers(Request $request, $courseId)
    {
        $course = Course::with(['enrollments.user'])->findOrFail($courseId);
        
        $users = $course->enrollments->map(function($enrollment) {
            return [
                'id' => $enrollment->user->id,
                'name' => $enrollment->user->name,
                'email' => $enrollment->user->email,
                'phone' => $enrollment->user->phone ?? 'N/A',
                'id_no' => $enrollment->user->id_no ?? 'N/A',
                'kmlttb_no' => $enrollment->user->kmlttb_no ?? 'N/A',
                'enrolled_at' => $enrollment->enrolled_at->format('Y-m-d H:i:s'),
                'status' => $enrollment->status,
                'progress' => $enrollment->progress,
            ];
        });
        
        return response()->json(['users' => $users]);
    }

    /**
     * Get detailed attendance information for a specific meeting (AJAX)
     */
    public function getMeetingAttendance(Request $request, ZoomMeeting $meeting)
    {
        $meeting->load(['attendances.user']);
        
        $attendances = $meeting->attendances->map(function($attendance) {
            return [
                'id' => $attendance->id,
                'user_name' => $attendance->user->name,
                'user_email' => $attendance->user->email,
                'id_no' => $attendance->user->id_no ?? 'N/A',
                'kmlttb_no' => $attendance->user->kmlttb_no ?? 'N/A',
                'joined_at' => $attendance->joined_at ? $attendance->joined_at->format('Y-m-d H:i:s') : 'N/A',
                'left_at' => $attendance->left_at ? $attendance->left_at->format('Y-m-d H:i:s') : 'N/A',
                'duration_minutes' => $attendance->duration_minutes,
                'compliance_status' => $attendance->duration_minutes >= 45 ? 'Compliant' : 'Non-Compliant',
                'attended_full_session' => $attendance->attended_full_session,
            ];
        });
        
        return response()->json(['attendances' => $attendances]);
    }

    /**
     * Calculate CPD hours based on attendance duration
     */
    private function calculateCpdHours($durationMinutes)
    {
        // KMLTTB requires minimum 45 minutes for CPD credit
        if ($durationMinutes < 45) {
            return 0;
        }

        // Convert minutes to hours, rounded to 1 decimal place
        return round($durationMinutes / 60, 1);
    }

    /**
     * Get KMLTTB compliance summary
     */
    public function getKmlttbComplianceSummary()
    {
        $totalUsers = User::whereNotNull('kmlttb_no')->where('kmlttb_no', '!=', '')->count();
        $totalAttendances = MeetingAttendance::whereHas('user', function($q) {
            $q->whereNotNull('kmlttb_no')->where('kmlttb_no', '!=', '');
        })->count();

        $compliantAttendances = MeetingAttendance::whereHas('user', function($q) {
            $q->whereNotNull('kmlttb_no')->where('kmlttb_no', '!=', '');
        })->where('duration_minutes', '>=', 45)->count();

        $complianceRate = $totalAttendances > 0 ? round(($compliantAttendances / $totalAttendances) * 100, 2) : 0;

        $totalCpdHours = MeetingAttendance::whereHas('user', function($q) {
            $q->whereNotNull('kmlttb_no')->where('kmlttb_no', '!=', '');
        })->where('duration_minutes', '>=', 45)
        ->get()
        ->sum(function($attendance) {
            return $this->calculateCpdHours($attendance->duration_minutes);
        });

        return [
            'total_kmlttb_users' => $totalUsers,
            'total_attendances' => $totalAttendances,
            'compliant_attendances' => $compliantAttendances,
            'compliance_rate' => $complianceRate,
            'total_cpd_hours' => round($totalCpdHours, 1),
            'average_cpd_per_user' => $totalUsers > 0 ? round($totalCpdHours / $totalUsers, 1) : 0
        ];
    }

    /**
     * Format currency in KES with comma separators
     */
    private function formatKes($amount)
    {
        return 'KES ' . number_format($amount, 0);
    }

    /**
     * Get regulatory compliance indicators
     */
    public function getRegulatoryComplianceIndicators()
    {
        $indicators = [];

        // Check KMLTTB registration compliance
        $totalUsers = User::count();
        $kmlttbUsers = User::whereNotNull('kmlttb_no')->where('kmlttb_no', '!=', '')->count();
        $kmlttbRegistrationRate = $totalUsers > 0 ? round(($kmlttbUsers / $totalUsers) * 100, 2) : 0;

        $indicators['kmlttb_registration'] = [
            'rate' => $kmlttbRegistrationRate,
            'status' => $kmlttbRegistrationRate >= 90 ? 'excellent' : ($kmlttbRegistrationRate >= 75 ? 'good' : 'needs_improvement'),
            'message' => $kmlttbRegistrationRate >= 90 ? 'Excellent KMLTTB registration compliance' :
                        ($kmlttbRegistrationRate >= 75 ? 'Good KMLTTB registration rate' : 'KMLTTB registration needs improvement')
        ];

        // Check attendance compliance
        $complianceSummary = $this->getKmlttbComplianceSummary();
        $indicators['attendance_compliance'] = [
            'rate' => $complianceSummary['compliance_rate'],
            'status' => $complianceSummary['compliance_rate'] >= 80 ? 'excellent' :
                       ($complianceSummary['compliance_rate'] >= 60 ? 'good' : 'needs_improvement'),
            'message' => $complianceSummary['compliance_rate'] >= 80 ? 'Excellent attendance compliance' :
                        ($complianceSummary['compliance_rate'] >= 60 ? 'Good attendance compliance' : 'Attendance compliance needs improvement')
        ];

        // Check CPD hours distribution
        $avgCpdHours = $complianceSummary['average_cpd_per_user'];
        $indicators['cpd_hours'] = [
            'average' => $avgCpdHours,
            'status' => $avgCpdHours >= 20 ? 'excellent' : ($avgCpdHours >= 10 ? 'good' : 'needs_improvement'),
            'message' => $avgCpdHours >= 20 ? 'Excellent CPD hours accumulation' :
                        ($avgCpdHours >= 10 ? 'Good CPD hours progress' : 'CPD hours accumulation needs improvement')
        ];

        return $indicators;
    }
}
