<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\User;
use App\Models\Enrollment;
use App\Models\ZoomMeeting;
use App\Models\MeetingAttendance;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminDashboardController extends Controller
{
    /**
     * Display the admin dashboard with analytics
     */
    public function index()
    {
        // Get key metrics for the dashboard
        $totalCourses = Course::where('status', 'published')->count();
        $totalEnrollments = Enrollment::count();
        $totalRevenue = Order::where('payment_status', 'paid')->sum('total');
        $totalMeetings = ZoomMeeting::count();
        
        // Recent enrollments (last 30 days)
        $recentEnrollments = Enrollment::where('enrolled_at', '>=', Carbon::now()->subDays(30))->count();
        
        // Attendance compliance rate
        $totalAttendances = MeetingAttendance::count();
        $compliantAttendances = MeetingAttendance::where('duration_minutes', '>=', 45)->count();
        $complianceRate = $totalAttendances > 0 ? round(($compliantAttendances / $totalAttendances) * 100, 2) : 0;
        
        // Top performing courses by enrollment
        $topCourses = Course::withCount('enrollments')
            ->where('status', 'published')
            ->orderBy('enrollments_count', 'desc')
            ->limit(5)
            ->get();
        
        // Monthly revenue trend (last 6 months)
        $monthlyRevenue = Order::where('payment_status', 'paid')
            ->where('created_at', '>=', Carbon::now()->subMonths(6))
            ->selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, SUM(total) as revenue')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();

        // Recent orders for dashboard table
        $recentOrders = Order::with(['user', 'orderItems.course'])
            ->where('payment_status', 'paid')
            ->latest()
            ->limit(10)
            ->get();

        // KMLTTB compliance data
        $kmlttbCompliance = $this->getKmlttbComplianceSummary();
        $regulatoryIndicators = $this->getRegulatoryComplianceIndicators();

        // Recent activity (enrollments and orders)
        $recentActivity = $this->getRecentActivity();

        return view('admin.dashboard-analytics', compact(
            'totalCourses',
            'totalEnrollments',
            'totalRevenue',
            'totalMeetings',
            'recentEnrollments',
            'complianceRate',
            'topCourses',
            'monthlyRevenue',
            'recentOrders',
            'kmlttbCompliance',
            'regulatoryIndicators',
            'recentActivity'
        ));
    }

    /**
     * Get KMLTTB compliance summary
     */
    private function getKmlttbComplianceSummary()
    {
        $totalUsers = User::whereNotNull('kmlttb_no')->where('kmlttb_no', '!=', '')->count();
        $totalAttendances = MeetingAttendance::whereHas('user', function($q) {
            $q->whereNotNull('kmlttb_no')->where('kmlttb_no', '!=', '');
        })->count();

        $compliantAttendances = MeetingAttendance::whereHas('user', function($q) {
            $q->whereNotNull('kmlttb_no')->where('kmlttb_no', '!=', '');
        })->where('duration_minutes', '>=', 45)->count();

        $complianceRate = $totalAttendances > 0 ? round(($compliantAttendances / $totalAttendances) * 100, 2) : 0;

        $totalCpdHours = MeetingAttendance::whereHas('user', function($q) {
            $q->whereNotNull('kmlttb_no')->where('kmlttb_no', '!=', '');
        })->where('duration_minutes', '>=', 45)
        ->get()
        ->sum(function($attendance) {
            return $this->calculateCpdHours($attendance->duration_minutes);
        });

        return [
            'total_kmlttb_users' => $totalUsers,
            'total_attendances' => $totalAttendances,
            'compliant_attendances' => $compliantAttendances,
            'compliance_rate' => $complianceRate,
            'total_cpd_hours' => round($totalCpdHours, 1),
            'average_cpd_per_user' => $totalUsers > 0 ? round($totalCpdHours / $totalUsers, 1) : 0
        ];
    }

    /**
     * Get regulatory compliance indicators
     */
    private function getRegulatoryComplianceIndicators()
    {
        $indicators = [];

        // Check KMLTTB registration compliance
        $totalUsers = User::count();
        $kmlttbUsers = User::whereNotNull('kmlttb_no')->where('kmlttb_no', '!=', '')->count();
        $kmlttbRegistrationRate = $totalUsers > 0 ? round(($kmlttbUsers / $totalUsers) * 100, 2) : 0;

        $indicators['kmlttb_registration'] = [
            'rate' => $kmlttbRegistrationRate,
            'status' => $kmlttbRegistrationRate >= 90 ? 'excellent' : ($kmlttbRegistrationRate >= 75 ? 'good' : 'needs_improvement'),
            'message' => $kmlttbRegistrationRate >= 90 ? 'Excellent KMLTTB registration compliance' :
                        ($kmlttbRegistrationRate >= 75 ? 'Good KMLTTB registration rate' : 'KMLTTB registration needs improvement')
        ];

        // Check attendance compliance
        $totalAttendances = MeetingAttendance::count();
        $compliantAttendances = MeetingAttendance::where('duration_minutes', '>=', 45)->count();
        $attendanceComplianceRate = $totalAttendances > 0 ? round(($compliantAttendances / $totalAttendances) * 100, 2) : 0;

        $indicators['attendance_compliance'] = [
            'rate' => $attendanceComplianceRate,
            'status' => $attendanceComplianceRate >= 80 ? 'excellent' : ($attendanceComplianceRate >= 60 ? 'good' : 'needs_improvement'),
            'message' => $attendanceComplianceRate >= 80 ? 'Excellent attendance compliance' :
                        ($attendanceComplianceRate >= 60 ? 'Good attendance compliance' : 'Attendance compliance needs improvement')
        ];

        return $indicators;
    }

    /**
     * Get recent activity for dashboard
     */
    private function getRecentActivity()
    {
        $activities = [];

        // Recent enrollments
        $recentEnrollments = Enrollment::with(['user', 'course'])
            ->latest()
            ->limit(5)
            ->get();

        foreach ($recentEnrollments as $enrollment) {
            $activities[] = [
                'type' => 'enrollment',
                'message' => "{$enrollment->user->name} enrolled in {$enrollment->course->title}",
                'time' => $enrollment->enrolled_at,
                'icon' => 'uil-graduation-cap',
                'color' => 'success'
            ];
        }

        // Recent orders
        $recentOrders = Order::with(['user'])
            ->where('payment_status', 'paid')
            ->latest()
            ->limit(5)
            ->get();

        foreach ($recentOrders as $order) {
            $activities[] = [
                'type' => 'order',
                'message' => "{$order->user->name} completed order #{$order->id} - KES " . number_format($order->total, 0),
                'time' => $order->created_at,
                'icon' => 'uil-shopping-cart',
                'color' => 'primary'
            ];
        }

        // Sort by time and limit to 10
        usort($activities, function($a, $b) {
            return $b['time'] <=> $a['time'];
        });

        return array_slice($activities, 0, 10);
    }

    /**
     * Calculate CPD hours based on attendance duration
     */
    private function calculateCpdHours($durationMinutes)
    {
        if ($durationMinutes < 45) {
            return 0;
        }
        
        return round($durationMinutes / 60, 1);
    }

    /**
     * Format currency in KES with comma separators
     */
    private function formatKes($amount)
    {
        return 'KES ' . number_format($amount, 0);
    }
}
