@extends('admin.dashboard')
@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">

            <!-- start page title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-flex align-items-center justify-content-between">
                        <h4 class="mb-0">Admin Dashboard</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="javascript: void(0);">Lernovate</a></li>
                                <li class="breadcrumb-item active">Dashboard</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end page title -->

            <!-- Key Metrics Cards -->
            <div class="row">
                <div class="col-md-6 col-xl-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="float-end mt-2">
                                <div class="avatar-sm">
                                    <span class="avatar-title bg-primary rounded-circle">
                                        <i class="uil-graduation-cap font-size-16"></i>
                                    </span>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-1 mt-1"><span data-plugin="counterup">{{ $totalCourses }}</span></h4>
                                <p class="text-muted mb-0">Published Courses</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-xl-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="float-end mt-2">
                                <div class="avatar-sm">
                                    <span class="avatar-title bg-success rounded-circle">
                                        <i class="uil-users-alt font-size-16"></i>
                                    </span>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-1 mt-1"><span data-plugin="counterup">{{ $totalEnrollments }}</span></h4>
                                <p class="text-muted mb-0">Total Enrollments</p>
                            </div>
                            <p class="text-muted mt-3 mb-0">
                                <span class="text-success me-1">
                                    <i class="mdi mdi-arrow-up-bold me-1"></i>{{ $recentEnrollments }}
                                </span> in last 30 days
                            </p>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-xl-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="float-end mt-2">
                                <div class="avatar-sm">
                                    <span class="avatar-title bg-warning rounded-circle">
                                        <i class="uil-money-bill font-size-16"></i>
                                    </span>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-1 mt-1">KES <span data-plugin="counterup">{{ number_format($totalRevenue, 0) }}</span></h4>
                                <p class="text-muted mb-0">Total Revenue</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-xl-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="float-end mt-2">
                                <div class="avatar-sm">
                                    <span class="avatar-title bg-info rounded-circle">
                                        <i class="uil-video font-size-16"></i>
                                    </span>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-1 mt-1"><span data-plugin="counterup">{{ $totalMeetings }}</span></h4>
                                <p class="text-muted mb-0">Total Meetings</p>
                            </div>
                            <p class="text-muted mt-3 mb-0">
                                <span class="text-{{ $complianceRate >= 80 ? 'success' : ($complianceRate >= 60 ? 'warning' : 'danger') }} me-1">
                                    <i class="mdi mdi-arrow-{{ $complianceRate >= 80 ? 'up' : 'down' }}-bold me-1"></i>{{ $complianceRate }}%
                                </span> compliance rate
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- KMLTTB Compliance Overview -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title mb-4">KMLTTB Compliance Overview</h4>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="avatar-sm me-3">
                                            <span class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                                <i class="uil-users-alt font-size-18"></i>
                                            </span>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">{{ $kmlttbCompliance['total_kmlttb_users'] }}</h5>
                                            <p class="text-muted mb-0">KMLTTB Registered Users</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="avatar-sm me-3">
                                            <span class="avatar-title rounded-circle bg-success-subtle text-success">
                                                <i class="uil-check-circle font-size-18"></i>
                                            </span>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">{{ $kmlttbCompliance['compliant_attendances'] }}</h5>
                                            <p class="text-muted mb-0">Compliant Attendances</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="avatar-sm me-3">
                                            <span class="avatar-title rounded-circle bg-warning-subtle text-warning">
                                                <i class="uil-clock font-size-18"></i>
                                            </span>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">{{ $kmlttbCompliance['total_cpd_hours'] }}</h5>
                                            <p class="text-muted mb-0">Total CPD Hours</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="avatar-sm me-3">
                                            <span class="avatar-title rounded-circle bg-info-subtle text-info">
                                                <i class="uil-percentage font-size-18"></i>
                                            </span>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">{{ $kmlttbCompliance['compliance_rate'] }}%</h5>
                                            <p class="text-muted mb-0">Overall Compliance Rate</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4 p-3 bg-light rounded">
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <h6 class="text-muted mb-1">Regulatory Body</h6>
                                        <p class="mb-0 font-weight-bold">KMLTTB</p>
                                    </div>
                                    <div class="col-md-3">
                                        <h6 class="text-muted mb-1">Minimum Attendance</h6>
                                        <p class="mb-0 font-weight-bold">45 Minutes</p>
                                    </div>
                                    <div class="col-md-3">
                                        <h6 class="text-muted mb-1">CPD Requirement</h6>
                                        <p class="mb-0 font-weight-bold">Annual Credits</p>
                                    </div>
                                    <div class="col-md-3">
                                        <h6 class="text-muted mb-1">Compliance Status</h6>
                                        <p class="mb-0">
                                            <span class="badge badge-soft-{{ $complianceRate >= 80 ? 'success' : ($complianceRate >= 60 ? 'warning' : 'danger') }}">
                                                {{ $complianceRate >= 80 ? 'Excellent' : ($complianceRate >= 60 ? 'Good' : 'Needs Improvement') }}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title mb-4">Quick Actions</h4>
                            
                            <div class="d-grid gap-2">
                                <a href="{{ route('admin.reports.index') }}" class="btn btn-primary">
                                    <i class="uil-chart-line me-2"></i>View Full Reports
                                </a>
                                <a href="{{ route('admin.reports.attendance') }}" class="btn btn-outline-primary">
                                    <i class="uil-video me-2"></i>Meeting Attendance
                                </a>
                                <a href="{{ route('admin.reports.registrations') }}" class="btn btn-outline-success">
                                    <i class="uil-users-alt me-2"></i>Course Registrations
                                </a>
                                <a href="{{ route('admin.courses.index') }}" class="btn btn-outline-info">
                                    <i class="uil-graduation-cap me-2"></i>Manage Courses
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Courses and Recent Activity -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title mb-4">Top Performing Courses</h4>
                            
                            @forelse($topCourses as $course)
                            <div class="d-flex align-items-center mb-3">
                                <div class="avatar-sm me-3">
                                    <span class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                        {{ $loop->iteration }}
                                    </span>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ $course->title }}</h6>
                                    <p class="text-muted mb-0">{{ $course->enrollments_count }} enrollments</p>
                                </div>
                                <div>
                                    <span class="badge badge-soft-success">{{ $course->enrollments_count }}</span>
                                </div>
                            </div>
                            @empty
                            <div class="text-center text-muted">
                                <i class="uil-graduation-cap font-size-24 mb-2 d-block"></i>
                                <p>No course data available</p>
                            </div>
                            @endforelse
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title mb-4">Recent Activity</h4>
                            
                            <div data-simplebar style="max-height: 300px;">
                                @forelse($recentActivity as $activity)
                                <div class="d-flex align-items-start mb-3">
                                    <div class="avatar-sm me-3">
                                        <span class="avatar-title rounded-circle bg-{{ $activity['color'] }}-subtle text-{{ $activity['color'] }}">
                                            <i class="{{ $activity['icon'] }} font-size-16"></i>
                                        </span>
                                    </div>
                                    <div class="flex-grow-1">
                                        <p class="mb-1">{{ $activity['message'] }}</p>
                                        <p class="text-muted mb-0 font-size-13">{{ $activity['time']->diffForHumans() }}</p>
                                    </div>
                                </div>
                                @empty
                                <div class="text-center text-muted">
                                    <i class="uil-clock font-size-24 mb-2 d-block"></i>
                                    <p>No recent activity</p>
                                </div>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div> <!-- container-fluid -->
    </div>
    <!-- End Page-content -->

    <footer class="footer">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <script>document.write(new Date().getFullYear())</script> © Lernovate CPD Platform.
                </div>
                <div class="col-sm-6">
                    <div class="text-sm-end d-none d-sm-block">
                        KMLTTB Certified CPD Provider
                    </div>
                </div>
            </div>
        </div>
    </footer>
</div>
@endsection
